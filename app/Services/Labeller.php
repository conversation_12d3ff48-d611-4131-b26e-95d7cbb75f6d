<?php

namespace App\Services;

use App\Enums\CollectionType;
use App\Enums\SourceType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Labeller
{
    public static function mutate(string $label): string
    {

        $label = strtolower($label);

        $specialLabels = [
            SourceType::YOUTUBE->value => 'Video',
            SourceType::EPISODE->value => 'Podcast Episode',
            SourceType::URL->value => 'Web Page',
            CollectionType::RSS->value => 'RSS Feed',
            CollectionType::CHANNEL->value => 'YouTube Channel',
        ];
        $acronyms = [SourceType::URL->value];

        if (isset($specialLabels[$label])) {
            return $specialLabels[$label];
        }

        foreach ($acronyms as $acronym) {
            if (
                ($label == $acronym)/* ||
                str_contains($acronym . ' ', $label) ||
                str_contains(' ' . $acronym, $label)*/
            ) {
                $label = str_replace($acronym, strtoupper($acronym), $label);
            }
        }

        $label = str_replace('_', ' ', $label);
        $label = ucwords($label);
        $label = str_replace('Id', 'ID', $label);

        return $label;

    }

    public static function model(Model|string $model): string
    {
        return Str::title(Str::snake(class_basename($model), ' '));
    }

}
