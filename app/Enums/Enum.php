<?php

namespace App\Enums;

use App\Services\Labeller;

trait Enum
{
    public static function keys(): array
    {
        return array_column(self::cases(), 'name');
    }

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public static function toArray(): array
    {
        return array_combine(self::keys(), self::values());
    }

    public static function asOptions(): array
    {
        return array_combine(self::values(), array_map(Labeller::class.'::mutate', self::keys()));
    }
}
