<?php
namespace App\Models;

use Altek\Accountant\Contracts\Recordable;
use Altek\Accountant\Recordable as RecordsToLedger;
use Altek\Eventually\Eventually as RecordsPivotsToLedger;
use App\Enums\AgentApiResponseFormat;
use App\Enums\AgentReasoningEffort;
use App\Enums\AgentVerbosity;
use App\Models\Traits\BelongsToTeam;
use App\Models\Traits\BelongsToUser;
use App\Models\Traits\CanBeLocked;
use App\Models\Traits\CanBeModerated;
use App\Models\Traits\HasCategories;
use App\Models\Traits\HasClassification;
use App\Models\Traits\HasImage;
use App\Models\Traits\HasSlug;
use App\Models\Traits\SyncsToFrontend;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use RingleSoft\LaravelProcessApproval\Contracts\ApprovableModel;
use Spatie\Tags\HasTags;
use Spatie\Translatable\HasTranslations;
use willvincent\Rateable\Rateable;

class Agent extends Model implements ApprovableModel, Recordable
{
    use BelongsToTeam;
    use BelongsToUser;
    use CanBeLocked;
    use CanBeModerated;
    use HasCategories;
    use HasClassification;
    use HasFactory;
    use HasImage;
    use HasSlug;
    use HasTranslations;
    use Rateable;
    use RecordsPivotsToLedger;
    use RecordsToLedger;
    use SoftDeletes;
    use SyncsToFrontend;
    use HasTags;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [

        // Summary
        'name',

        // Domain
        'slug',
        'root_domain',
        'vanity_domain',

        // Options
        'has_semantic_search',
        'is_nonbillable',
        'max_memories',

        // Model
        'model_id',
        'model_system_prompt',
        'model_context_prompt',
        'model_max_tokens',
        'model_temperature',
        'model_top_p',
        'model_frequency_penalty',
        'model_presence_penalty',
        'model_reasoning_effort',
        'model_verbosity',

        // Persona
        'persona_name',
        'persona_tagline',
        'persona_description',
        'persona_avatar_path',

        // Status
        'is_active',
        'is_locked',
        'is_open_source',
        'is_selectable',
        'is_extensible',
        'is_template',
        'is_approved',
        'parent_id',

        // Marketplace
        'marketplace_active',
        'target_worldview',

        // Languages & Translations
        'default_language',
        'default_translation',
        'supported_languages',
        'auto_translate',
        'auto_translate_languages',
        'supported_translations',

        // Corpus
        'use_team_corpus',
        'disable_community_corpus',
        'classification_id',

        // API
        'api_response_format',
        'lock_system_prompt',
        'strip_markdown',

        // Owner
        'user_id',
        'team_id',

        // Debug & Sort
        'debug',
        'seq',

    ];

    /**
     * @var string[]
     */
    public $translatable = [

        // Marketplace
        'target_worldview',

        // Persona
        'persona_name',
        'persona_tagline',
        'persona_description',

    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [

            // Options
            'has_semantic_search' => 'boolean',
            'is_nonbillable' => 'boolean',
            'max_memories' => 'integer',

            // Model
            'model_temperature' => 'float',
            'model_top_p' => 'float',
            'model_frequency_penalty' => 'float',
            'model_presence_penalty' => 'float',
            'model_reasoning_effort' => AgentReasoningEffort::class,
            'model_verbosity' => AgentVerbosity::class,

            // Status
            'is_active' => 'boolean',
            'is_locked' => 'boolean',
            'is_open_source' => 'boolean',
            'is_selectable' => 'boolean',
            'is_extensible' => 'boolean',
            'is_template' => 'boolean',
            'is_approved' => 'boolean',

            // Marketplace
            'marketplace_active' => 'boolean',
            'target_worldview' => 'array',

            // Languages & Translations
            'supported_languages' => 'array',
            'auto_translate' => 'boolean',
            'auto_translate_languages' => 'array',
            'supported_translations' => 'array',

            // Corpus
            'use_team_corpus' => 'boolean',
            'disable_community_corpus' => 'boolean',

            // API
            'api_response_format' => AgentApiResponseFormat::class,
            'lock_system_prompt' => 'boolean',
            'strip_markdown' => 'boolean',

            // Debug & Sort
            'debug' => 'boolean',
            'seq' => 'integer',

        ];
    }

    public static function booted(): void
    {
        parent::booted();
        static::creating(function (Model $model) {
            $model->is_nonbillable = $model->team->is_nonbillable;
        });
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(static::class, 'parent_id');
    }

    public function questions(): HasMany
    {
        return $this->hasMany(AgentQuestion::class);
    }

    public function integrations(): HasMany
    {
        return $this->hasMany(AgentIntegration::class);
    }

    public function ctas(): HasMany
    {
        return $this->hasMany(AgentCta::class);
    }

    public function tokens(): HasMany
    {
        return $this->hasMany(AgentToken::class);
    }

    public function collections(): BelongsToMany
    {
        return $this->belongsToMany(Collection::class);
    }

    public function contributors(): BelongsToMany
    {
        return $this->belongsToMany(Contributor::class);
    }

    public function sources(): BelongsToMany
    {
        return $this->belongsToMany(Source::class);
    }

    public function model(): BelongsTo
    {
        return $this->belongsTo(AgentModel::class, 'model_id');
    }

    public function frontend(): HasOne
    {
        return $this->hasOne(AgentFrontend::class);
    }

    public function syncToFrontend(): void
    {

        increase_timeout('MAX');

        $this->pushFieldsToFrontend();
        $this->pruneDeletedFromFrontend();

        $this->load(['categories', 'collections', 'contributors', 'sources', 'tags']);

        DB::connection(env('FRONTEND_DB_CONNECTION'))
            ->table($this->getTable())
            ->where('id', $this->id)
            ->update([
                'categories' => $this->categories()->pluck('categories.id'),
                'collections' => $this->collections()->pluck('collections.id'),
                'contributors' => $this->contributors()->pluck('contributors.id'),
                'sources' => $this->sources()->pluck('sources.id'),
                'tags' => $this->tags()->pluck('tags.id'),
            ])
        ;

    }

    public static function getSupportedLanguages(
        ?array $selectedLanguages = null,
        ?bool $autoTranslate = null,
        ?string $modelId = null,
        ?Agent $agent = null
    ): array {

        // Load up all the languages
        $allLanguages = config('agent.languages');

        // Allow model's saved attributes to be overridden (e.g., in a dirty form)
        $selectedLanguages = $selectedLanguages ?? $agent->supported_languages ?? [];

        // If supported languages are explicitly defined, return those
        if (! empty($selectedLanguages)) {
            return Arr::only(
                $allLanguages,
                $selectedLanguages
            );
        }

        // Otherwise fallback to available languages
        return static::getAvailableLanguages($autoTranslate, $modelId, $agent);

    }

    public static function getRealTimeTranslationLanguages(
        ?array $supportedLanguages = null,
        ?array $excludeLanguages = []
    ): array {

        // Load up all the languages
        $languages = Arr::except(
            config('agent.languages'),
            array_merge(
                $excludeLanguages,
                config('agent.exclude_auto_languages')
            )
        );

        // Restrict down to supported languages if
        if ($supportedLanguages) {
            $languages = Arr::only($languages, $supportedLanguages);
        }

        return $languages;

    }

    public static function getAvailableLanguages(?bool $autoTranslate = null, ?int $modelId = null, ?Agent $agent = null): array
    {

        // Load up all the languages
        $allLanguages = config('agent.languages');

        // Allow model's saved attributes to be overridden (e.g., in a dirty form)
        $autoTranslate = $autoTranslate ?? $agent->auto_translate ?? false;
        $modelId = $modelId ?? $agent?->model_id ?? null;

        // If the Agent is set to auto translate, return all possible languages
        if ($autoTranslate) {
            return $allLanguages;
        }

        // Otherwise fallback to the languages that the model supports
        return Arr::only(
            $allLanguages,
            $modelId ? static::getModelLanguages($modelId) : [env('AGENT_DEFAULT_LANGUAGE')]
        );

    }

    public static function getModelLanguages(int $modelId): array
    {
        return AgentModel::find($modelId)->languages;
    }

    public static function getSupportedTranslations(?array $selectedTranslations = null, ?Agent $agent = null): array
    {

        // Load up all the languages
        $allTranslations = config('agent.translations');

        // Allow model's saved attributes to be overridden (e.g., in a dirty form)
        $selectedTranslations = $selectedTranslations ?? $agent->supported_translations ?? [];

        // If supported translations are explicitly defined, return those
        if (! empty($selectedTranslations)) {
            return Arr::only(
                $allTranslations,
                $selectedTranslations
            );
        }

        // Otherwise fallback to all translations
        return $allTranslations;

    }

    public function getCreditUsage()
    {
        $numCredits = $this->model->num_credits;
        if ($this->auto_translate) {
            $numCredits += env_int('REALTIME_TRANSLATION_CREDITS');
        }
        return $numCredits;
    }

    public function getUrl(bool $excludeProtocol = false): string
    {

        if ($this->vanity_domain) {
            $domain = $this->vanity_domain;
        } else {
            $domain = "{$this->slug}.{$this->root_domain}";
        }

        return $excludeProtocol ? $domain : "https://{$domain}";

    }

    public static function getTagClassName(): string
    {
        return Tag::class;
    }

    public function tags(): MorphToMany
    {
        return $this
            ->morphToMany(self::getTagClassName(), 'taggable', 'taggables', null, 'tag_id')
            ->team()
            ->orderBy('order_column')
        ;
    }

}
