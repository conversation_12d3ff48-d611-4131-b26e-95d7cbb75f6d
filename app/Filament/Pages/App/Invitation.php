<?php

namespace App\Filament\Pages\App;

use App\Models\Membership;
use App\Models\Team;
use App\Models\TeamInvitation;
use App\Models\User;
use Filament\Pages\SimplePage;
use Filament\Support\Colors\Color;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;

class Invitation extends SimplePage
{
    protected static string $view = 'pages.app.accept-invitation';

    protected bool $isValid = false;

    protected ?User $user;

    protected ?TeamInvitation $invitation;

    protected ?Team $team;

    protected ?Membership $membership;

    public function __construct()
    {
        if (request()->hasValidSignature()) {

            $this->isValid = true;
            $this->user = auth()->user();

            $this->invitation = TeamInvitation::find(request()->input('id'));
            if (is_null($this->invitation->accepted_at)) {
                $this->invitation->update(['accepted_at' => $this->invitation->freshTimestamp()]);
            }

            $this->team = $this->invitation->team;

            // Create the membership if it doesn't already exist
            $this->membership = Membership::firstOrNew([
                'model_id' => $this->user->id,
                'team_id' => $this->team->id,
            ]);
            if (! $this->membership->exists) {
                $this->membership->role_id = $this->invitation->role_id;
                $this->membership->save();
            }

            // If the user hasn't verified their email, treat this as verification
            if (! $this->user->hasVerifiedEmail()) {
                $this->user->markEmailAsVerified();
            }

        }
    }

    public function getTitle(): string|Htmlable
    {
        return $this->isValid ? new HtmlString("Invitation to Join Team:<br>{$this->team->name}") : 'Invalid Invitation';
    }

    /**
     * @return array|mixed[]
     */
    protected function getViewData(): array
    {
        return [
            'isValid' => $this->isValid,
            'invitation' => $this->isValid ? $this->invitation : null,
            'membership' => $this->isValid ? $this->membership : null,
            'user' => $this->isValid ? $this->user : null,
            'actionUrl' => $this->isValid ?
                route('filament.app.tenant.switch', ['tenant' =>  $this->team->id]) :
                route('filament.id.auth.login')
            ,
            'actionColor' => Color::hex(env('AGENT_DEFAULT_PRIMARY_COLOR')),
        ];
    }
}
