<?php

namespace App\Filament\Templates;

use App\Filament\Traits\CanBeLocked;
use App\Filament\Traits\HasActions;
use App\Filament\Traits\HasProductTour;
use App\Models\Traits\CanBeModerated;
use App\Services\Gatekeeper;
use EightyNine\Approvals\Forms\Actions\ApproveAction;
use EightyNine\Approvals\Forms\Actions\RejectAction;
use EightyNine\Approvals\Forms\Actions\SubmitAction;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\ReplicateAction;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord as BaseEditRecord;
use Google\Cloud\Core\Exception\GoogleException;
use Google\Cloud\Core\Exception\ServiceException;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Database\Eloquent\Model;
use JibayMcs\FilamentTour\Tour\HasTour;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class EditRecord extends BaseEditRecord
{
    use HasActions;
    use HasProductTour;
    use HasTour;

    protected $listeners = ['refresh' => 'refreshForm'];

    protected static ?string $contributeTemplate = null;

    public function getTitle(): string|Htmlable
    {
        return $this->getRecordTitle();
    }

    /**
     * @return array|Action[]|ActionGroup[]
     */
    protected function getHeaderActions(): array
    {
        return array_merge(
            uses_trait(static::$resource, CanBeModerated::class) ? $this->getModerationHeaderActions() : [],
            uses_trait(static::$resource, CanBeLocked::class) ? $this->getLockActions() : [],
        );
    }

    protected function getLockActions(): array
    {
        return [
            $this->decorateLockAction(Action::make('Lock'), static::$resource)
                ->after(fn ($livewire) => $livewire->dispatch('$refresh')),
            $this->decorateUnlockAction(Action::make('Unlock'), static::$resource)
                ->after(fn ($livewire) => $livewire->dispatch('$refresh')),
        ];
    }

    /**
     * @return array|Action[]|ActionGroup[]
     */
    protected function getFormActions(): array
    {
        return [
            $this->getSaveFormAction(),
            $this->getCancelFormAction(),
            $this->getFormActionArchive(),
            $this->getFormActionRestore(),
            $this->getFormActionDelete(),
        ];
    }

    protected function getSaveFormAction(): Action
    {
        return parent::getSaveFormAction()
            ->extraAttributes(['id' => btn_id('save')])
            ->label('Save Changes')
            ->keyBindings(null)
            ->hidden(fn ($record) => restrict_editing($record))
            ->after(function () {
                $this->dispatch('$refresh');
            })
        ;
    }

    protected function getCancelFormAction(): Action
    {
        return parent::getCancelFormAction()
            ->label(fn ($record) => restrict_editing($record) ? 'Back' : 'Cancel')
            ->keyBindings(null)
            ->extraAttributes(['id' => btn_id('cancel')])
        ;
    }

    /**
     * @return DeleteAction
     */
    protected function getFormActionArchive(): Actions\DeleteAction
    {
        return Actions\DeleteAction::make('archive')
            ->label('Archive')
            ->color('danger')
            ->outlined()
            ->icon('heroicon-s-archive-box')
            ->modalIcon('heroicon-s-archive-box')
            ->modalHeading(fn (Model $record): string => "Archive {$record->name}")
            ->keyBindings(null)
            ->extraAttributes(['class' => 'ml-auto', 'id' => btn_id('archive')])
            ->successNotificationTitle('Archived')
            ->hidden(fn ($record) => $record->trashed() || ! Gatekeeper::userCan('delete', $this->getModel(), $record))
        ;
    }

    protected function getFormActionRestore(): Actions\RestoreAction
    {
        return Actions\RestoreAction::make('restore')
            ->label('Restore')
            ->icon('heroicon-s-arrow-path-rounded-square')
            ->color('success')
            ->outlined()
            ->modalIcon('heroicon-s-arrow-path-rounded-square')
            ->keyBindings(null)
            ->extraAttributes(['class' => 'ml-auto mr-0', 'id' => btn_id('restore')])
            ->hidden(fn ($record) => ! $record->trashed() || ! Gatekeeper::userCan('restore', $this->getModel(), $record))
        ;
    }

    protected function getFormActionDelete(): Actions\ForceDeleteAction
    {
        return Actions\ForceDeleteAction::make('delete')
            ->label('Delete')
            ->icon('heroicon-s-trash')
            ->modalHeading(fn (Model $record): string => "Permanently Delete {$record->name}")
            ->modalDescription('Are you sure? This cannot be undone.')
            ->visible($this->getRecord()->trashed() && Gatekeeper::userCan('force_delete', $this->getModel(), $this->getRecord()))
            ->keyBindings(null)
//            ->extraAttributes(['class'=>'ml-auto', 'id'=>btn_id('force-delete')])
            ->successNotificationTitle('Deleted')
            ->hidden(fn ($record) => ! $record->trashed() && Gatekeeper::userCan('force_delete', $this->getModel(), $record))
        ;
    }

    protected function getFormActionSuspend(): Actions\DeleteAction
    {
        return Actions\DeleteAction::make('suspend')
            ->label('Suspend')
            ->outlined()
            ->icon('heroicon-s-pause-circle')
            ->modalIcon('heroicon-s-pause-circle')
            ->modalHeading(fn (Model $record): string => "Suspend {$record->name}")
            ->successNotificationTitle('Suspended')
            ->keyBindings(null)
            ->extraAttributes(['class' => 'ml-auto', 'id' => btn_id('suspend')])
        ;
    }

    protected function getFormActionReactivate(): Actions\RestoreAction
    {
        return Actions\RestoreAction::make('reactivate')
            ->label('Reactivate')
            ->icon('heroicon-s-play-circle')
            ->color('success')
            ->outlined()
            ->modalIcon('heroicon-s-play-circle')
            ->modalHeading(fn (Model $record): string => "Reactivate {$record->name}")
            ->successNotificationTitle('Reactivated')
            ->keyBindings(null)
            ->extraAttributes(['id' => btn_id('reactivate')])
        ;
    }

    /**
     * @return array|Action[]|ActionGroup[]
     */
    protected function getModerationHeaderActions(): array
    {
        return [
            $this->getModerationActionApprove(),
            $this->getModerationActionReject(),
            $this->getModerationActionSubmit(),
            $this->getModerationActionAutoSubmit(),
            $this->getModerationActionResubmit(),
        ];
    }

    protected function getModerationActionApprove(): ApproveAction
    {
        return ApproveAction::make('approve')
            ->extraAttributes(['id' => btn_id('approve')])
            ->label('Approve')
            ->icon('heroicon-o-check-circle')
            ->color('success')
            ->modalIcon('heroicon-o-check-circle')
        ;
    }

    protected function getModerationActionReject(): RejectAction
    {
        return RejectAction::make('reject')
            ->extraAttributes(['id' => btn_id('reject')])
            ->label('Reject')
            ->icon('heroicon-o-x-circle')
            ->modalIcon('heroicon-o-x-circle')
        ;
    }

    protected function getModerationActionSubmit(): SubmitAction
    {
        return SubmitAction::make('submit')
            ->extraAttributes(['id' => btn_id('submit')])
            ->label('Contribute')
            ->icon('heroicon-o-arrow-up-on-square')
            ->color('success')
            ->outlined()
            ->modalIcon('heroicon-o-arrow-up-on-square')
            ->modalDescription(new HtmlString(view(
                !is_null(static::$contributeTemplate) ? static::$contributeTemplate : 'components.app.contribute-default',
                [
                    'model' => Str::title(static::$resource::getModelLabel()),
                    'socialUrl' => env('SOCIAL_URL'),
                ]
            )))
        ;
    }

    protected function getModerationActionAutoSubmit(): Action
    {
        return Action::make('autosubmit')
            ->extraAttributes(['id' => btn_id('autosubmit')])
            ->label('Contribute Source')
            ->icon('heroicon-o-arrow-up-on-square')
            ->color('success')
            ->outlined()
            ->visible(fn ($record) => ! $record->isSubmitted() &&
                Gatekeeper::userCan('moderate', static::$resource::getModel()) &&
                ! $record->isApprovalCreator()
            )
            ->action(function ($record, $livewire) {
                $record->autoSubmit(get_current_user());
                Notification::make()
                    ->title('Submitted successfully')
                    ->success()
                    ->send();
                $livewire->dispatch('$refresh');

                return true;
            })
            ->requiresConfirmation()
            ->modalIcon('heroicon-o-clock')
        ;
    }

    protected function getModerationActionResubmit(): Action
    {
        return Action::make('resubmit')
            ->extraAttributes(['id' => btn_id('resubmit')])
            ->label('Resubmit for Review')
            ->icon('heroicon-o-clock')
            ->color('success')
            ->visible(fn ($record) => $record->isApprovalCompleted() && $record->isRejected())
            ->action(function ($record, $livewire) {
                $record->undoLastApproval();
                Notification::make()
                    ->title('Resubmitted successfully')
                    ->success()
                    ->send();
                $livewire->dispatch('$refresh');

                return true;
            })
            ->requiresConfirmation()
            ->modalIcon('heroicon-o-clock')
        ;
    }

    protected static function getReplicateHeaderAction(?array $formFields = null, array $excludeAttributes = []): Action
    {
        if (is_null($formFields)) {
            $formFields = [
                static::getFormFieldName()
                    ->required(),
            ];
        }
        return ReplicateAction::make('duplicate')
            ->extraAttributes(['id' => btn_id('duplicate')])
            ->label('Duplicate')
            ->icon('heroicon-s-document-duplicate')
            ->outlined()
            ->mutateRecordDataUsing(function (array $data): array {
                $data['name'] = "{$data['name']} copy";
                return $data;
            })
            ->requiresConfirmation()
            ->form($formFields)
            ->modalHeading(fn ($record) => "Duplicate {$record->name}")
            ->modalIcon('heroicon-s-document-duplicate')
            ->excludeAttributes($excludeAttributes)
            ->successRedirectUrl(fn (Model $replica): string => static::$resource::getUrl('edit', ['record' => $replica->id]))
            ->successNotificationTitle(fn ($record) => "{$record->name} duplicated")
            ->visible(fn ($record) => Gatekeeper::userCan('replicate', static::$resource::getModel()))
        ;
    }

    public function refreshForm(): void
    {
        $this->fillForm();
    }

    /**
     * @throws GoogleException
     * @throws ServiceException
     */
    public function translateFormField(string $language, string|array|null $currentState, ?string $savedState): string
    {
        return translate_field($language, $currentState, $savedState);
    }

}
