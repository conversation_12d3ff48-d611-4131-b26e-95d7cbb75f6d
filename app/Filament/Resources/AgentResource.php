<?php

namespace App\Filament\Resources;

use App\Filament\RelationManagers\HistoryRelationManager;
use App\Filament\Resources\AgentResource\Pages\EditFrontendRecord;
use App\Filament\Resources\AgentResource\Pages\EditRecord;
use App\Filament\Resources\AgentResource\RelationManagers\AgentCtasRelationManager;
use App\Filament\Resources\AgentResource\RelationManagers\AgentIntegrationsRelationManager;
use App\Filament\Resources\AgentResource\RelationManagers\AgentQuestionsRelationManager;
use App\Filament\Resources\AgentResource\RelationManagers\AgentTokensRelationManager;
use App\Filament\Resources\Resource as BaseResource;
use App\Filament\Traits\CanBeAdministered;
use App\Filament\Traits\CanBeLocked;
use App\Filament\Traits\CanBeModerated;
use App\Filament\Traits\CanBeOrdered;
use App\Filament\Traits\CanBePromoted;
use App\Filament\Traits\CanBeReplicated;
use App\Models\Agent;
use App\Services\Gatekeeper;
use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Exception;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Pages\Page;
use Filament\Resources\Pages\PageRegistration;
use Filament\Tables\Actions\Action as TableAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class AgentResource extends BaseResource implements HasShieldPermissions
{
    use CanBeAdministered;
    use CanBeLocked;
    use CanBeModerated;
    use CanBeOrdered;
    use CanBePromoted;
    use CanBeReplicated;
    use Translatable;

    protected static ?string $model = Agent::class;

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $navigationIcon = 'heroicon-s-sparkles';

    protected static ?string $pluralModelLabel = 'Agents';

    protected static string $createLabel = 'create';

    protected static array $customPermissions = [
        'test',
    ];

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    /**
     * @throws Exception
     */
    public static function table(Table $table): Table
    {
        return static::decorateOrderableTable($table
            ->modifyQueryUsing(function (Builder $query) {
                static::$isScopedToTenant = false;
                if (! Gatekeeper::userCanAdminister(static::getModel())) {
                    $query->whereBelongsTo(get_current_team());
                }
            })
            ->columns([
                static::getTableColumnName(),
                TextColumn::make('target_worldview')
                    ->label('Target Worldview'),
                static::getTableColumnActive(),
                static::getTableColumnCreated(),
                static::getTableColumnUpdated(),
            ])
            ->filters([
                static::getTableFilterBoolean('is_active', 'Active'),
                static::getTableFilterBoolean('is_locked', 'Locked', 'Unlocked'),
                static::getTableFilterRelation('classification', 'Denominational Alignment'),
                static::getTableFilterArchived(),
                static::getTableFilterTeam(),
            ])
            ->actions([
                static::decorateLockAction(TableAction::make('Lock')),
                static::decorateUnlockAction(TableAction::make('Unlock')),
                static::decorateViewAction(
                    TableAction::make('View'),
                    fn ($record) => $record->is_active ? 'View Agent' : 'Agent Not Active',
                    fn ($record) => $record->is_active,
                    fn ($record) => $record->getUrl()
                ),
                static::getTableActionEditRow(),
            ])
            ->bulkActions(static::getTableBulkActions())
            ->paginated(static::$pageOptions)
            ->defaultPaginationPageOption(static::$defaultPageOption)
        );
    }


    /**
     * @return string[]
     */
    public static function getRelations(): array
    {
        return [
            AgentQuestionsRelationManager::class,
            AgentCtasRelationManager::class,
            AgentIntegrationsRelationManager::class,
            AgentTokensRelationManager::class,
            HistoryRelationManager::class,
        ];
    }

    /**
     * @return array|PageRegistration[]
     *
     * @throws Exception
     */
    public static function getPages(): array
    {
        return array_merge(
            parent::getPages(),
            ['edit-frontend' => EditFrontendRecord::route('/{record}/edit-frontend')]
        );
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            EditRecord::class,
            EditFrontendRecord::class,
        ]);
    }

}
