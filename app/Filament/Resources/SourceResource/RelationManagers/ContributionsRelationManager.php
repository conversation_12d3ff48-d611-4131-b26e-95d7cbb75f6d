<?php

namespace App\Filament\Resources\SourceResource\RelationManagers;

use App\Filament\Resources\ContributorResource;
use App\Filament\Templates\RelationManager;
use App\Filament\Traits\CanBeOrdered;
use App\Filament\Traits\HasActions;
use App\Filament\Traits\HasColumns;
use App\Filament\Traits\HasFields;
use App\Filament\Traits\HasFilters;
use App\Models\Contributor;
use Exception;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ContributionsRelationManager extends RelationManager
{
    use HasActions;
    use HasColumns;
    use HasFields;
    use HasFilters;
    use CanBeOrdered;

    protected static string $relationship = 'contributions';

    protected static ?string $title = 'Contributors';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('contributor_id')
                    ->label('Contributor')
                    ->required()
                    ->relationship('contributor', 'name')
                    ->searchable()
                    ->getSearchResultsUsing(function (string $search, RelationManager $livewire): array {
                        return Contributor::where('name', 'like', "%{$search}%")
                            ->whereNotIn('id', $livewire->getOwnerRecord()->contributions->pluck('contributor_id'))
                            ->limit(50)
                            ->pluck('name', 'id')
                            ->toArray();
                    }),
                static::getFormFieldContributionRole(),
            ])
            ->disabled(fn ($record) => restrict_editing($this->getOwnerRecord()));
    }

    /**
     * @throws Exception
     */
    public function table(Table $table): Table
    {
        $restrictEditingClosure = fn ($record) => restrict_editing($this->getOwnerRecord());
        return static::decorateOrderableTable($table
            ->recordTitleAttribute('contributor.name')
            ->columns([
                static::getTableColumnTextLink(
                    'contributor.name',
                    'Name',
                    fn ($record) => ContributorResource::getUrl('edit', ['record' => $record->contributor_id])
                ),
                static::getTableColumnContributionRole()
                    ->visibleFrom('md'),
                static::getTableColumnCreated(),
                static::getTableColumnUpdated(),
            ])
            ->filters([
                static::getTableFilterContributionRole(),
                static::getTableFilterArchived(),
            ])
            ->headerActions([
                static::getTableCreateAction('Contributor')
                    ->disabled($restrictEditingClosure),
            ])
            ->actions(static::getRelationManagerTableActions('Contributor', $this->getOwnerRecord()))
            ->modifyQueryUsing(fn (Builder $query) => $query->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]))
        );
    }
}
