<?php

namespace App\Filament\Resources\TeamResource\RelationManagers;

use App\Filament\RelationManagers\TeamRelationManager;
use App\Filament\Traits\HasActions;
use App\Filament\Traits\HasColumns;
use App\Filament\Traits\HasFields;
use App\Filament\Traits\HasFilters;
use App\Models\User;
use App\Services\Gatekeeper;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class MembershipRelationManager extends TeamRelationManager
{
    use HasActions;
    use HasColumns;
    use HasFields;
    use HasFilters;

    protected static string $relationship = 'memberships';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return 'Members';
    }

    public function form(Form $form): Form
    {
        $restrictEditing = restrict_editing($this->getOwnerRecord());

        return $form
            ->schema([
                Forms\Components\Select::make('model_id')
                    ->label('User')
                    ->required()
                    ->relationship('model', 'name')
                    ->searchable()
                    ->getSearchResultsUsing(function (string $search, RelationManager $livewire): array {
                        return User::where('name', 'like', "%{$search}%")
                            ->whereNot('id', $livewire->getOwnerRecord()->model_id)
                            ->whereNot('id', $livewire->getOwnerRecord()->user_id)
                            ->whereNotIn('id', $livewire->getOwnerRecord()->memberships->pluck('id'))
                            ->limit(50)
                            ->pluck('name', 'id')
                            ->toArray();
                    })
                    ->disabled(fn (?Model $record) => ! is_null($record)),
                static::getFormFieldRole(),
            ])
            ->disabled($restrictEditing);
    }

    /**
     * @throws \Exception
     */
    public function table(Table $table): Table
    {
        $ownerRecord = $this->getOwnerRecord();
        $restrictEdit = restrict_editing($ownerRecord);
        $restrictEditClosure = function ($record) use ($ownerRecord) {
            return restrict_editing($ownerRecord) || ($record->model_id === $record->team->user_id);
        };

        return $table
            ->recordTitleAttribute('model.name')
            ->columns([
                static::getTableColumnName('SUSPENDED', 'model.name'),
                static::getTableColumnEmail('model.email'),
                static::getTableColumnRole(),
                static::getTableColumnCreated(),
                static::getTableColumnUpdated(),
            ])
            ->filters([
                static::getTableFilterRelation('role'),
                static::getTableFilterActive(),
                static::getTableFilterSuspended(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make('add')
                    ->extraAttributes(['id' => btn_id('add-member')])
                    ->label('Add Team Member')
                    ->visible(Gatekeeper::userCanAdminister(static::getOwnerRecord()))
                    ->modalHeading('Add Team Member')
                    ->modalSubmitActionLabel('Add Team Member')
                    ->extraModalFooterActions(function ($action): array {
                        return [
                            $action->makeModalSubmitAction('createAnother', ['another' => true])
                                ->label('Add and Add Another'),
                        ];
                    })
                    ->successNotificationTitle('Team Member Added')
                    ->hidden($restrictEdit),
            ])
            ->actions([
                static::getTableActionEdit()
                    ->hidden($restrictEditClosure)
                    ->modalHeading('Edit Team Member'),
                static::getTableActionSuspend()
                    ->hidden($restrictEditClosure),
                static::getTableActionReactivate()
                    ->hidden($restrictEditClosure),
                static::getTableActionDelete()
                    ->hidden($restrictEditClosure),
            ])
            ->modifyQueryUsing(function (Builder $query) {
                $query->withoutGlobalScopes([
                    SoftDeletingScope::class,
                ]);
            })
        ;
    }

}
