<?php

namespace App\Filament\Resources\AgentResource\RelationManagers;

use App\Filament\Templates\RelationManager;
use App\Filament\Traits\HasActions;
use App\Filament\Traits\HasColumns;
use App\Filament\Traits\HasFields;
use App\Filament\Traits\HasFilters;
use Exception;
use Filament\Facades\Filament;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Support\Enums\IconPosition;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\HtmlString;

class AgentTokensRelationManager extends RelationManager
{
    use HasActions;
    use HasColumns;
    use HasFields;
    use HasFilters;

    protected static string $relationship = 'tokens';

    protected static ?string $title = 'API Keys';

    protected static string $createLabel = 'create';

//    protected static bool $isLazy = false;

    protected $listeners = ['refreshRelation' => '$refresh'];

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool
    {
        return get_current_team()->getPlanOption('agent.api');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')
                    ->label('Name')
                    ->required(),
                static::getFormFieldActive('
                    You may deactivate an API key temporarily, making it unusable.
                    Or, revoke the API key to completely remove it.
                ')
                    ->default(true),
            ]);
    }

    /**
     * @throws Exception
     */
    public function table(Table $table): Table
    {
        $restrictEditingClosure = fn () => restrict_editing($this->getOwnerRecord());

        return $table
            ->heading('API Keys')
            ->defaultSort('name')
            ->columns([
                TextColumn::make('name'),
                static::getTableColumnActive(),
                Tables\Columns\TextColumn::make('token')
                    ->label('API Key')
                    ->copyable()
                    ->copyMessage('Copied!')
                    ->copyMessageDuration(2500)
                    ->icon('heroicon-o-clipboard')
                    ->iconPosition(IconPosition::After)
                    ->formatStateUsing(fn ($state) => new HtmlString("<span class='text-gray-600 dark:text-gray-400'>".str_repeat('x', 32).'</span>'))
                ,
                static::getTableColumnCreated(),
                static::getTableColumnUpdated(),
            ])
            ->filters([
                static::getTableFilterRevoked(),
            ])
            ->headerActions([
                static::getTableCreateAction('API Key')
                    ->hidden(restrict_editing($this->getOwnerRecord())),
            ])
            ->emptyStateHeading('No API Keys')
            ->emptyStateDescription('Create an API key to get started.')
            ->actions([
                static::getTableActionEdit()
                    ->hidden($restrictEditingClosure),
                static::getTableActionRevoke()
                    ->hidden($restrictEditingClosure),
                static::getTableActionReactivate()
                    ->hidden($restrictEditingClosure),
                static::getTableActionDelete()
                    ->hidden($restrictEditingClosure),
            ])
            ->modifyQueryUsing(fn (Builder $query) => $query->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]));
    }
}
