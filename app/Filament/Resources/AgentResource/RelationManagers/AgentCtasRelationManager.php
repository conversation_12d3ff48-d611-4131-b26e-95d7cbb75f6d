<?php

namespace App\Filament\Resources\AgentResource\RelationManagers;

use App\Enums\AgentCtaResponseBasis;
use App\Enums\AgentCtaTimingMode;
use App\Filament\Templates\RelationManager;
use App\Filament\Traits\CanBeOrdered;
use App\Filament\Traits\HasActions;
use App\Filament\Traits\HasColumns;
use App\Filament\Traits\HasFields;
use App\Filament\Traits\HasFilters;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\Concerns\Translatable;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;


class AgentCtasRelationManager extends RelationManager
{
    use HasActions;
    use HasColumns;
    use HasFields;
    use HasFilters;
    use CanBeOrdered;
    use Translatable;

    protected static string $relationship = 'ctas';

    protected static ?string $title = 'Calls to Action';

//    protected static bool $isLazy = false;



    protected $listeners = ['refreshRelation' => '$refresh'];

    protected static string $orderColumn = 'priority';

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool
    {
        return get_current_team()?->getPlanOption('agent.white_label');
    }

    public function form(Form $form): Form
    {

        $displayModes = [
            'footer' => [
                'label' => 'Response Footer',
                'description' => 'Show below the response.',
                'icon' => 'heroicon-o-bars-arrow-down',
            ],
            'modal' => [
                'label' => 'Modal',
                'description' => 'Show in a modal overlaying response.',
                'icon' => 'heroicon-o-window',
            ],
        ];

        $timings = [
            'threshold' => [
                'label' => 'From Threshold Onwards',
                'description' => 'Always show starting from response {n}.',
                'icon' => 'heroicon-o-arrow-right-circle',
            ],
            'always' => [
                'label' => 'Always',
                'description' => 'Show on every response.',
                'icon' => 'heroicon-o-arrow-path',
            ],
            'interval' => [
                'label' => 'At Interval',
                'description' => 'Show on every {n} responses.',
                'icon' => 'heroicon-o-clock',
            ],
            'once' => [
                'label' => 'Only Once',
                'description' => 'Show once on response {n}.',
                'icon' => 'heroicon-o-cursor-arrow-rays',
            ],
        ];

        return $form
            ->schema([

                Tabs::make('Tabs')
                    ->tabs([

                        Tab::make('Summary')
                            ->schema([
                                static::getFormFieldName()
                                    ->columnSpan(1)
                                ,
                                static::getFormFieldActive('
                                    You can temporarily deactivate this call to action without removing it.
                                    Only active calls to action will surface.
                                ')
                                    ->columnSpan(1)
                                ,
                            ])
                            ->columns(['sm' => 1, 'lg' => 2])
                        ,

                        Tab::make('What')
                            ->schema([
                                static::getFormFieldHtmlEditorBasic('content', 'Content')
                                    ->hintIcon(
                                        'heroicon-s-question-mark-circle',
                                        tooltip: 'You can inject merge field values into the content (e.g., in link URLs) — as per the key below.'
                                    )
                                    ->helperText(view('components.help.agent-cta-merge'))
                                    ->columnSpan('full')
                                ,
                            ])
                            ->columns(['sm' => 1, 'lg' => 2])
                        ,

                        Tab::make('Where')
                            ->schema([
                                static::getFormFieldActive(
                                    null,
                                    'standalone_active',
                                    'Show in Standalone Mode'
                                )
                                    ->default(true)
                                    ->live()
                                    ->columnSpan(1)
                                ,
                                static::getFormFieldActive(
                                    null,
                                    'embedded_active',
                                    'Show in Embedded Mode'
                                )
                                    ->default(true)
                                    ->live()
                                    ->columnSpan(1)
                                ,
                                static::getFormFieldCardSelector('display_mode', 'Display Mode', $displayModes)
                                    ->default('footer')
                                    ->hintIcon(
                                        'heroicon-s-question-mark-circle',
                                        tooltip: 'Only applicable when active in Standalone or Embedded mode.'
                                    )
                                    ->visible(fn (Get $get) => $get('standalone_active') || $get('embedded_active'))
                                    ->columnSpan('full')
                                ,
                                static::getFormFieldActive(
                                    null,
                                    'api_active',
                                    'Include in API Responses'
                                )
                                    ->columnSpan(1)
                                ,
                                CheckboxList::make('integrations')
                                    ->relationship(titleAttribute: 'name')
                                    ->hintIcon(
                                        'heroicon-s-question-mark-circle',
                                        tooltip: 'Integrations that don\'t support HTML will strip it automatically.'
                                    )
                                    ->bulkToggleable()
                                    ->visible(fn () => $this->getOwnerRecord()->integrations->count() > 0)
                                    ->columns(['sm' => 1, 'lg' => 2])
                                    ->columnSpan('full')
                                ,
                            ])
                            ->columns(['sm' => 1, 'lg' => 2])
                        ,

                        Tab::make('When')
                            ->schema([
                                static::getFormFieldCardSelector('timing_mode', 'Timing Mode', $timings)
                                    ->default('threshold')
                                    ->live()
                                    ->columnSpan('full')
                                ,
                                TextInput::make('response_number')
                                    ->label(function (Get $get) {
                                        return match ($get('timing_mode')) {
                                            AgentCtaTimingMode::THRESHOLD->value =>  'Response Threshold',
                                            AgentCtaTimingMode::INTERVAL->value => 'Response Interval',
                                            AgentCtaTimingMode::ONCE->value =>  'Response #',
                                            AgentCtaTimingMode::ALWAYS->value =>  null,
                                        };
                                    })
                                    ->visible(fn (Get $get) => $get('timing_mode') !== AgentCtaTimingMode::ALWAYS->value)
                                    ->numeric()
                                    ->step(1)
                                    ->minValue(1)
                                    ->maxValue(100)
                                    ->default(3)
                                    ->required()
                                    ->columnSpan(1)
                                ,
                                static::getFormFieldSelect('response_basis', AgentCtaResponseBasis::asOptions(), 'Response Basis')
                                    ->selectablePlaceholder(false)
                                    ->default(AgentCtaResponseBasis::CONVERSATION->value)
                                    ->visible(fn (Get $get) => $get('timing_mode') !== AgentCtaTimingMode::ALWAYS->value)
                                    ->columnSpan(1)
                                ,
                            ])
                            ->columns(['sm' => 1, 'lg' => 2])
                        ,

                    ])

            ])
            ->columns(1);

    }

    public function table(Table $table): Table
    {
        return $table
            ->heading(new HtmlString(Blade::render('Calls to Action <x-heroicon-s-language class="w-5 h-5 inline text-gray-400 dark:text-gray-500" />')))
            ->reorderable('priority')
            ->defaultSort('priority')
            ->columns([
                TextColumn::make('name'),
                static::getTableColumnActive(),
                TextColumn::make('timing_mode')
                    ->label('Timing Mode')
                    ->formatStateUsing(fn ($state) => ucwords($state->value))
                ,
                static::getTableColumnCreated(),
                static::getTableColumnUpdated(),
            ])
            ->filters([
                static::getTableFilterTernary('is_active', 'Active'),
                static::getTableFilterSelect('timing_mode', AgentCtaTimingMode::asOptions(), 'Timing Mode'),
                static::getTableFilterSelect('response_basis', AgentCtaResponseBasis::asOptions(), 'Response Basis'),
                static::getTableFilterTernary('standalone_active', 'Standalone Active'),
                static::getTableFilterTernary('embedded_active', 'Embedded Active'),
                static::getTableFilterTernary('api_active', 'API Active'),
                static::getTableFilterSelect(
                    'integrations',
                    $this->getOwnerRecord()->integrations->pluck('name', 'id')->toArray(),
                    'Integrations Active'
                )
                    ->query(function (Builder $query, array $state) {
                        if (! empty($state['values'])) {
                            $query->whereHas('integrations', function (Builder $query) use ($state) {
                                $query->whereIn('agent_integrations.id', $state['values']);
                            });
                        }
                    })
                ,
                static::getTableFilterArchived(),
            ])
            ->filtersFormColumns(3)
            ->headerActions([
                Tables\Actions\Action::make('translate')
                    ->label('Translate Calls to Action')
                    ->icon('heroicon-s-language')
                    ->outlined()
                    ->action(function () {
                        $this->getOwnerRecord()->ctas->each(function ($cta) {
                            $previousTranslation = $cta->getTranslation('content', $this->getActiveActionsLocale());
                            $newTranslation = translate_text($previousTranslation, $this->getActiveActionsLocale());
                            $cta->setTranslation('content', $this->getActiveActionsLocale(), $newTranslation);
                            $cta->save();
                        });
                        Notification::make()
                            ->title('Calls to Action translated')
                            ->success()
                            ->send();
                    })
                    ->after(function ($livewire) {
                        $livewire->dispatch('refreshRelation');
                    })
                    ->hidden(restrict_editing($this->getOwnerRecord())),
                static::getTableCreateAction('Call to Action')
                    ->hidden(restrict_editing($this->getOwnerRecord())),
            ])
            ->actions(static::getRelationManagerTableActions('Call to Action', $this->getOwnerRecord()))
            ->modifyQueryUsing(fn (Builder $query) => $query->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]))
        ;
    }

}
