<?php

namespace App\Filament\Resources\AgentResource\RelationManagers;

use App\Features\AgentIntegrations;
use App\Filament\Templates\RelationManager;
use App\Filament\Traits\HasActions;
use App\Filament\Traits\HasColumns;
use App\Filament\Traits\HasFields;
use App\Filament\Traits\HasFilters;
use App\Models\AgentIntegrationPlatform;
use Exception;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\Concerns\Translatable;
use Filament\Support\Enums\IconPosition;
use Filament\Tables;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use JaOcero\RadioDeck\Forms\Components\RadioDeck;
use Laravel\Pennant\Feature;
use Livewire\Attributes\Reactive;
use Illuminate\Database\Eloquent\Model;

class AgentIntegrationsRelationManager extends RelationManager
{
    use HasActions;
    use HasColumns;
    use HasFields;
    use HasFilters;
    use Translatable;

    protected static string $relationship = 'integrations';

    protected static ?string $title = 'Integrations';

//    protected static bool $isLazy = false;



    protected $listeners = ['refreshRelation' => '$refresh'];

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool
    {
        return (Feature::active(AgentIntegrations::class) && get_current_team()->getPlanOption('agent.integrations'));
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                static::getFormFieldPlatform(),
                static::getFormFieldName(),
                static::getFormFieldActive('
                    You can temporarily deactivate this integration without removing it.
                    Only active integrations will function.
                '),
                TextInput::make('account')
                    ->label('Platform Account ID')
                    ->required()
                    ->maxLength(250)
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: '
                            The account identifier with the platform for purposes of this integration.
                            e.g., The phone number associated with your WhatsApp business account.
                        '
                    )
                    ->visible(fn (Get $get) => $get('platform_id') && AgentIntegrationPlatform::find($get('platform_id'))->has_account)
                ,
                TextInput::make('secret')
                    ->label('Platform Account Secret')
//                    ->required()
                    ->maxLength(250)
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: 'The API key or secret token used to authenticate to the platform.'
                    )
                    ->required()
                    ->visible(fn (Get $get) => $get('platform_id') && AgentIntegrationPlatform::find($get('platform_id'))->has_secret)
                ,
                static::getFormFieldActive(
                    'Activate this integration automatically when this Agent loads.',
                    'auto_initialize',
                    'Auto-Initialize'
                )
                    ->visible(fn (Get $get) => $get('platform_id') && AgentIntegrationPlatform::find($get('platform_id'))->can_auto_initialize)
                ,
                TextInput::make('param_1')
                    ->label(fn (Get $get) => $get('platform_id') ? AgentIntegrationPlatform::find($get('platform_id'))->param_1_label : null)
                    ->maxLength(500)
                    ->visible(fn (Get $get) => $get('platform_id') && !empty(AgentIntegrationPlatform::find($get('platform_id'))->param_1_label))
                ,
                TextInput::make('param_2')
                    ->label(fn (Get $get) => $get('platform_id') ? AgentIntegrationPlatform::find($get('platform_id'))->param_2_label : null)
                    ->maxLength(500)
                    ->visible(fn (Get $get) => $get('platform_id') && !empty(AgentIntegrationPlatform::find($get('platform_id'))->param_2_label))
                ,
                TextInput::make('param_3')
                    ->label(fn (Get $get) => $get('platform_id') ? AgentIntegrationPlatform::find($get('platform_id'))->param_3_label : null)
                    ->maxLength(500)
                    ->visible(fn (Get $get) => $get('platform_id') && !empty(AgentIntegrationPlatform::find($get('platform_id'))->param_3_label))
                ,
                static::getFormFieldLanguages($this->getOwnerRecord()->supported_languages),
                TextInput::make('cue')
                    ->label(new HtmlString(Blade::render('Cue Phrase <x-heroicon-s-language class="w-5 h-5 inline text-gray-400 dark:text-gray-500" />')))
                    ->maxLength(50)
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: '
                            A "Cue Phrase" is a signal to prompt your Agent to respond to a given message. e.g., "Hey Aquinas."
                            If entered, your Agent will only respond if the the Cue Phrase is mentioned in a message.
                            Otherwise, your Agent will always respond to every message.
                            This value is translatable per language.
                        '
                    )
                    ->visible(fn (Get $get) => $get('platform_id') && AgentIntegrationPlatform::find($get('platform_id'))->has_cue)
                ,
                Textarea::make('welcome')
                    ->label(new HtmlString(Blade::render('Welcome Message <x-heroicon-s-language class="w-5 h-5 inline text-gray-400 dark:text-gray-500" />')))
                    ->rows(3)
                    ->live(onBlur: true)
                    ->columnSpan(['default' => 2, 'md' => 1])
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: '
                            This message will be displayed right away when a user engages with your Agent.
                            Or, for messaging platforms that support welcome templates, enter the template ID here.
                            This value is translatable per language.
                        '
                    )
                    ->visible(fn (Get $get) => $get('platform_id') && AgentIntegrationPlatform::find($get('platform_id'))->has_welcome)
                ,
            ])
        ;
    }

    /**
     * @throws Exception
     */
    public function table(Table $table): Table
    {
        return $table
            ->heading(new HtmlString(Blade::render('Integrations <x-heroicon-s-language class="w-5 h-5 inline text-gray-400 dark:text-gray-500" />')))
            ->columns([
                TextColumn::make('name'),
                static::getTableColumnActive(),
                ImageColumn::make('platform.image_path')->height(24),
                Tables\Columns\TextColumn::make('platform.endpoint_url')
                    ->label('Endpoint')
                    ->copyable()
                    ->copyableState(fn ($state, $record) => replace_tokens($state, ['domain'=>$record->agent->getUrl(true), 'id'=>$record->id]))
                    ->copyMessage('Copied!')
                    ->copyMessageDuration(2500)
                    ->icon('heroicon-o-clipboard')
                    ->iconPosition(IconPosition::After)
                    ->formatStateUsing(fn ($state, $record) => new HtmlString(
                        "<span class='text-gray-600 dark:text-gray-400'>" .
                        Str::limit(replace_tokens($state, ['domain'=>$record->agent->getUrl(true), 'id'=>$record->id]), 25) .
                        "</span>"
                    ))
                    ->visibleFrom('lg')
                ,
                static::getTableColumnCreated(),
                static::getTableColumnUpdated(),
            ])
            ->filters([
                static::getTableFilterArchived(),
            ])
            ->headerActions([
                Tables\Actions\Action::make('translate')
                    ->extraAttributes(['id' => btn_id('translate-all-integrations')])
                    ->label('Translate Integrations')
                    ->icon('heroicon-s-language')
                    ->outlined()
                    ->action(function () {
                        $this->getOwnerRecord()->integrations->each(function ($integration) {
                            $fields = ['cue', 'welcome'];
                            foreach ($fields as $field)
                            {
                                $previousTranslation = $integration->getTranslation($field, $this->getActiveActionsLocale());
                                $newTranslation = translate_text($previousTranslation, $this->getActiveActionsLocale());
                                $integration->setTranslation($field, $this->getActiveActionsLocale(), $newTranslation);
                            }
                            $integration->save();
                        });
                        Notification::make()
                            ->title('Integrations translated')
                            ->success()
                            ->send();
                    })
                    ->after(function ($livewire) {
                        $livewire->dispatch('refreshRelation');
                    })
                    ->hidden(restrict_editing($this->getOwnerRecord())),
                static::getTableCreateAction('Integration')
                    ->hidden(restrict_editing($this->getOwnerRecord())),
            ])
            ->actions(static::getRelationManagerTableActions('Integration', $this->getOwnerRecord()))
            ->modifyQueryUsing(fn (Builder $query) => $query->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]));
    }

    protected static function getFormFieldPlatform(): RadioDeck
    {

        $platforms = AgentIntegrationPlatform::where('is_active', true)->orderBy('name', 'desc')->get();
        $platformCards = [];
        foreach ($platforms as $platform) {
            $platformCards[$platform->id]['label'] = '';
            $platformCards[$platform->id]['description'] = $platform->description;
            $platformCards[$platform->id]['icon'] = get_path_url($platform->image_path);
        }

        return static::getFormFieldCardSelector('platform_id', false, $platformCards)
            ->default(array_key_first($platformCards))
            ->iconSizes([ // Customize the values for each icon size
                'sm' => 'h-12 w-full',
                'md' => 'h-12 w-full',
                'lg' => 'h-12 w-full',
            ])
            ->padding('px-4 py-2')
            ->extraCardsAttributes([
                'class' => '!bg-white peer-checked:ring-4'
            ])
            ->live()
        ;

    }

    protected static function getFormFieldSupportedLanguages(): Select
    {
        return static::getFormFieldLanguages(

        );
    }

}
