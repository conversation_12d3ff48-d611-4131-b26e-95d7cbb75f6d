<?php

namespace App\Filament\Resources\ContributorResource\RelationManagers;

use App\Enums\SourceType;
use App\Filament\Resources\ArticleSourceResource;
use App\Filament\Resources\BookSourceResource;
use App\Filament\Resources\EpisodeSourceResource;
use App\Filament\Resources\MediaSourceResource;
use App\Filament\Resources\UrlSourceResource;
use App\Filament\Resources\YoutubeSourceResource;
use App\Filament\Templates\RelationManager;
use App\Filament\Traits\HasActions;
use App\Filament\Traits\HasColumns;
use App\Filament\Traits\HasFields;
use App\Filament\Traits\HasFilters;
use App\Models\Source;
use Exception;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ContributionsRelationManager extends RelationManager
{
    use HasActions;
    use HasColumns;
    use HasFields;
    use HasFilters;

    protected static string $relationship = 'contributions';

    protected static ?string $title = 'Contributor Roles';

    const TYPE_RESOURCE_MAP = [
        SourceType::ARTICLE->value => ArticleSourceResource::class,
        SourceType::BOOK->value => BookSourceResource::class,
        SourceType::EPISODE->value => EpisodeSourceResource::class,
        SourceType::MEDIA->value => MediaSourceResource::class,
        SourceType::URL->value => UrlSourceResource::class,
        SourceType::YOUTUBE->value => YoutubeSourceResource::class,
    ];

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('source_id')
                    ->label('Source')
                    ->required()
                    ->relationship('source', 'name')
                    ->searchable()
                    ->getSearchResultsUsing(function (string $search, RelationManager $livewire): array {
                        return Source::where('name', 'like', "%{$search}%")
                            ->whereNotIn('id', $livewire->getOwnerRecord()->contributions->pluck('source_id'))
                            ->limit(50)
                            ->pluck('name', 'id')
                            ->toArray();
                    }),
                static::getFormFieldContributionRole(),
            ]);
    }

    /**
     * @throws Exception
     */
    public function table(Table $table): Table
    {
        return static::decorateOrderableTable($table
            ->recordTitleAttribute('source.name')
            ->columns([
                static::getTableColumnTextLink(
                    'source.name',
                    'Name',
                    function ($record) {
                        $resource = static::TYPE_RESOURCE_MAP[$record->source->type->value];
                        return $resource::getUrl('edit', ['record' => $record->source_id]);
                    }
                ),
                static::getTableColumnContributionRole()
                    ->visibleFrom('md'),
                static::getTableColumnCreated(),
                static::getTableColumnUpdated(),
            ])
            ->filters([
                static::getTableFilterContributionRole(),
                static::getTableFilterArchived(),
            ])
            ->headerActions([
                static::getTableCreateAction('Contributor Role')
                    ->hidden(restrict_editing($this->getOwnerRecord())),
            ])
            ->actions(static::getRelationManagerTableActions('Contributor Role', $this->getOwnerRecord()))
            ->modifyQueryUsing(fn (Builder $query) => $query->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]))
        );
    }
}
