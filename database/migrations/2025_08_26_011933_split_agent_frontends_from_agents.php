<?php

use App\Models\AgentFrontend;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{

    const array FRONTEND_COLUMN_MAP = [
        'color'             =>  'primary_color',
        'icon_path'         =>  'app_icon_path',
        'icon_color'        =>  'app_icon_color',
        'description'       =>  'creator_description',
        'beacon_icon_path'  =>  'embed_icon_path',
        'hide_header'       =>  'embed_hide_header',
    ];

    /**
     * Run the migrations.
     */
    public function up(): void
    {

        Schema::create('agent_frontends', function (Blueprint $table) {

            $table->id();
            $table->unsignedBigInteger('agent_id');
            $table->foreign('agent_id')->references('id')->on('agents');

            // Branding
            $table->string('image_path', 100)->nullable();
            $table->enum('theme', ['dark', 'light'])->nullable()->default('dark');
            $table->string('primary_color', 25)->nullable();
            $table->string('background_color', 25)->nullable();
            $table->string('background_path', 100)->nullable();
            $table->string('display_font_url', 250)->nullable();
            $table->string('display_font_name', 100)->nullable();
            $table->string('body_font_url', 250)->nullable();
            $table->string('body_font_name', 100)->nullable();

            // Meta
            $table->string('favicon_path', 100)->nullable();
            $table->jsonb('meta_title')->nullable();
            $table->jsonb('meta_description')->nullable();
            $table->jsonb('meta_keywords')->nullable();

            // App
            $table->string('app_icon_color', 25)->nullable();
            $table->string('app_icon_path', 100)->nullable();

            // Attribution
            $table->jsonb('creator_name')->nullable();
            $table->jsonb('creator_description')->nullable();
            $table->jsonb('creator_url')->nullable();

            // Intro
            $table->jsonb('intro_preamble')->nullable();
            $table->jsonb('intro_headline')->nullable();
            $table->jsonb('intro_description')->nullable();
            $table->jsonb('questions_title')->nullable();

            // Embed options
            $table->string('embed_icon_color', 25)->nullable();
            $table->string('embed_icon_path', 100)->nullable();
            $table->boolean('embed_hide_header')->default(false);

            // Media
            $table->boolean('show_media')->default(false);

            // Footer
            $table->jsonb('footer_text')->nullable();
            $table->boolean('hide_footer_cta')->default(false);
            $table->jsonb('footer_cta_label')->nullable();
            $table->jsonb('footer_cta_url')->nullable();

            // Custom Code
            $table->string('custom_styles', 1000)->nullable();
            $table->string('custom_scripts', 1000)->nullable();

            // Security
            $table->boolean('has_basic_auth')->default(false);
            $table->string('basic_auth_user', 50)->nullable();
            $table->string('basic_auth_password', 500)->nullable();

            $table->timestamps();
            $table->softDeletes();

        });

        Schema::create('agent_frontend_media_collection', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('agent_frontend_id');
            $table->foreign('agent_frontend_id')->references('id')->on('agent_frontends');
            $table->unsignedBigInteger('media_collection_id');
            $table->foreign('media_collection_id')->references('id')->on('collections');
        });

        $agents = DB::table('agents')->get();
        $frontendColumns = Schema::getColumnListing('agent_frontends');
        foreach ($agents as $agent) {

            // Migrate fields to frontends
            $attrs = rename_array_keys((array) $agent, static::FRONTEND_COLUMN_MAP);
            $attrs = array_merge(
                $attrs,
                ['agent_id' => $agent->id]
            );
            unset($attrs['id']);
            $frontendId = DB::table('agent_frontends')->insertGetId(Arr::only($attrs, $frontendColumns));

            // Migrate media collections to frontends
            $rels = DB::table('agent_media_collection')->where('agent_id', $agent->id)->get();
            foreach ($rels as $rel) {
                DB::table('agent_frontend_media_collection')->insert([
                    'agent_frontend_id' => $frontendId,
                    'media_collection_id' => $rel->collection_id,
                ]);
            }

        }

        Schema::table('agents', function (Blueprint $table) {

            $table->dropColumn('image_path');
            $table->dropColumn('theme');
            $table->dropColumn('color');
            $table->dropColumn('background_color');
            $table->dropColumn('background_path');
            $table->dropColumn('display_font_url');
            $table->dropColumn('display_font_name');
            $table->dropColumn('body_font_url');
            $table->dropColumn('body_font_name');

            // Meta
            $table->dropColumn('favicon_path');
            $table->dropColumn('meta_title');
            $table->dropColumn('meta_description');
            $table->dropColumn('meta_keywords');

            // App
            $table->dropColumn('icon_color');
            $table->dropColumn('icon_path');

            // Attribution
            $table->dropColumn('creator_name');
            $table->dropColumn('description');
            $table->dropColumn('creator_url');

            // Intro
            $table->dropColumn('intro_preamble');
            $table->dropColumn('intro_headline');
            $table->dropColumn('intro_description');
            $table->dropColumn('questions_title');

            // Embed options
            $table->dropColumn('beacon_icon_path');
            $table->dropColumn('hide_header');

            // Media
            $table->dropColumn('show_media');

            // Footer
            $table->dropColumn('footer_text');
            $table->dropColumn('hide_footer_cta');
            $table->dropColumn('footer_cta_label');
            $table->dropColumn('footer_cta_url');

            // Custom Code
            $table->dropColumn('custom_styles');
            $table->dropColumn('custom_scripts');

            // Security
            $table->dropColumn('has_basic_auth');
            $table->dropColumn('basic_auth_user');
            $table->dropColumn('basic_auth_password');

        });

        Schema::dropIfExists('agent_media_collection');

        // Sync frontends
        AgentFrontend::all()->each->syncToFrontend();

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

        Schema::table('agents', function (Blueprint $table) {

            // Branding
            $table->string('image_path', 100)->nullable();
            $table->enum('theme', ['dark', 'light'])->nullable()->default('dark');
            $table->string('color', 25)->nullable();
            $table->string('background_color', 25)->nullable();
            $table->string('background_path', 100)->nullable();
            $table->string('display_font_url', 250)->nullable();
            $table->string('display_font_name', 100)->nullable();
            $table->string('body_font_url', 250)->nullable();
            $table->string('body_font_name', 100)->nullable();

            // Meta
            $table->string('favicon_path', 100)->nullable();
            $table->jsonb('meta_title')->nullable();
            $table->jsonb('meta_description')->nullable();
            $table->jsonb('meta_keywords')->nullable();

            // App
            $table->string('icon_color', 25)->nullable();
            $table->string('icon_path', 100)->nullable();

            // Attribution
            $table->jsonb('creator_name')->nullable();
            $table->jsonb('description')->nullable();
            $table->jsonb('creator_url')->nullable();

            // Intro
            $table->jsonb('intro_preamble')->nullable();
            $table->jsonb('intro_headline')->nullable();
            $table->jsonb('intro_description')->nullable();
            $table->jsonb('questions_title')->nullable();

            // Embed options
            $table->string('beacon_icon_path', 100)->nullable();
            $table->boolean('hide_header')->default(false);

            // Media
            $table->boolean('show_media')->default(false);

            // Footer
            $table->jsonb('footer_text')->nullable();
            $table->boolean('hide_footer_cta')->default(false);
            $table->jsonb('footer_cta_label')->nullable();
            $table->jsonb('footer_cta_url')->nullable();

            // Custom Code
            $table->string('custom_styles', 1000)->nullable();
            $table->string('custom_scripts', 1000)->nullable();

            // Security
            $table->boolean('has_basic_auth')->default(false);
            $table->string('basic_auth_user', 50)->nullable();
            $table->string('basic_auth_password', 500)->nullable();

        });

        Schema::create('agent_media_collection', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('agent_id');
            $table->foreign('agent_id')->references('id')->on('agents');
            $table->unsignedBigInteger('collection_id');
            $table->foreign('collection_id')->references('id')->on('collections');
        });

        $frontends = DB::table('agent_frontends')->get();
        foreach ($frontends as $frontend) {

            // Migrate fields back to agents
            $attrs = rename_array_keys((array) $frontend, array_flip(static::FRONTEND_COLUMN_MAP));
            DB::table('agents')->where('id', $frontend->agent_id)->update(Arr::except($attrs, ['id', 'agent_id', 'embed_icon_color']));

            // Migrate media collections to frontends
            $rels = DB::table('agent_frontend_media_collection')->where('agent_frontend_id', $frontend->id)->get();
            foreach ($rels as $rel) {
                DB::table('agent_media_collection')->insert([
                    'agent_id' => $frontend->agent_id,
                    'collection_id' => $rel->media_collection_id,
                ]);
            }

        }

        Schema::dropIfExists('agent_frontend_media_collection');
        Schema::dropIfExists('agent_frontends');

    }

};
