<?php

use App\Enums\AgentCtaResponseBasis;
use App\Enums\AgentCtaTimingMode;
use App\Enums\AgentCtaDisplayMode;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {

        Schema::create('agent_ctas', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100);
            $table->unsignedBigInteger('agent_id');
            $table->foreign('agent_id')->references('id')->on('agents');
            $table->boolean('is_active')->default(false);
            $table->boolean('standalone_active')->default(true);
            $table->boolean('embedded_active')->default(true);
            $table->boolean('api_active')->default(true);
            $table->enum('display_mode', AgentCtaDisplayMode::values())->default(AgentCtaDisplayMode::FOOTER->value);
            $table->jsonb('content');
            $table->enum('timing_mode', AgentCtaTimingMode::values())->default(AgentCtaTimingMode::THRESHOLD->value);
            $table->tinyInteger('response_number')->unsigned()->nullable();
            $table->enum('response_basis', AgentCtaResponseBasis::values())->default(AgentCtaResponseBasis::CONVERSATION->value);
            $table->tinyInteger('priority')->unsigned()->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('agent_integration_cta', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('integration_id');
            $table->foreign('integration_id')->references('id')->on('agent_integrations');
            $table->unsignedBigInteger('cta_id');
            $table->foreign('cta_id')->references('id')->on('agent_ctas');
        });

        $agents = DB::table('agents')
            ->whereNotNull('response_footer_content')
            ->whereNotNull('response_footer_threshold')
            ->where('response_footer_content', '!=', '{"zu": ""}')
            ->get()
        ;
        foreach ($agents as $agent) {

            DB::table('agent_ctas')->insert([
                'name' => 'Default CTA',
                'agent_id' => $agent->id,
                'is_active' => true,
                'standalone_active' => true,
                'embedded_active' => true,
                'api_active' => true,
                'content' => $agent->response_footer_content,
                'timing_mode' => AgentCtaTimingMode::THRESHOLD->value,
                'response_number' => $agent->response_footer_threshold,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            $integrations = DB::table('agent_integrations')->whereNotNull('cta')->get();
            foreach ($integrations as $integration) {
                $ctaId = DB::table('agent_ctas')->insertGetId([
                    'name' => "{$integration->name} CTA",
                    'agent_id' => $agent->id,
                    'is_active' => $integration->is_active,
                    'standalone_active' => false,
                    'embedded_active' => false,
                    'api_active' => false,
                    'content' => $integration->cta,
                    'timing_mode' => AgentCtaTimingMode::THRESHOLD->value,
                    'response_number' => 1,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                DB::table('agent_integration_cta')->insert([
                    'integration_id' => $integration->id,
                    'cta_id' => $ctaId,
                ]);
            }

        }

        Schema::table('agents', function (Blueprint $table) {
            $table->dropColumn('response_footer_content');
            $table->dropColumn('response_footer_threshold');
        });

        Schema::table('agent_integrations', function (Blueprint $table) {
            $table->dropColumn('cta');
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

        Schema::table('agents', function (Blueprint $table) {
            $table->text('response_footer_content')->nullable()->after('intro_description');
            $table->tinyInteger('response_footer_threshold')->unsigned()->nullable()->after('response_footer_content');
        });

        Schema::table('agent_integrations', function (Blueprint $table) {
            $table->text('cta')->nullable()->after('welcome');
        });

        $ctas = DB::table('agent_ctas')->where('timing_mode', AgentCtaTimingMode::THRESHOLD->value)->get();
        foreach ($ctas as $cta)
        {

            if ($cta->standalone_active || $cta->embedded_active || $cta->api_active) {
                DB::table('agents')->where('id', $cta->agent_id)->update([
                    'response_footer_content' => $cta->content,
                    'response_footer_threshold' => $cta->response_number,
                ]);
            }

            $integrations = DB::table('agent_integrations')
                ->whereIn(
                    'id',
                    DB::table('agent_integration_cta')
                        ->where('cta_id', $cta->id)
                        ->pluck('integration_id')
                        ->toArray()
                )
                ->get()
            ;
            if (!empty($integrations)) {
                foreach ($integrations as $integration)
                {
                    DB::table('agent_integrations')->where('id', $integration->id)->update([
                        'cta' => $cta->content,
                    ]);
                }
            }

        }

        Schema::dropIfExists('agent_integration_cta');
        Schema::dropIfExists('agent_ctas');

    }
};
